package com.xyy.saas.inquiry.signature.api.signature;

import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.api.signature.dto.InquirySignatureImageDto;

/**
 * 签章合同 Api 接口
 *
 * <AUTHOR>
 */
public interface InquirySignatureImageApi {

    /**
     * 签章图片合并
     *
     * @param dto 合同类型
     * @return
     */
    String signatureImageMerge(InquirySignatureImageDto dto);

    /**
     * 获取远程审方签名图片url
     *
     * @param aLong
     * @param signaturePlatformEnum
     * @return
     */
    String getRemoteAuditSignatureUrl(Long aLong, SignaturePlatformEnum signaturePlatformEnum);
}