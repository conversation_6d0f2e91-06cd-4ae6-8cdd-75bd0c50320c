# 处方迁移服务使用指南

## 概述

处方迁移服务用于将历史处方数据从老系统迁移到Elasticsearch中，支持分批处理、进度跟踪和失败重试。

## 核心功能

### 1. 分批迁移
- 根据配置的页面大小（默认1000条）进行分批处理
- 支持按ID范围或机构号进行迁移
- 自动控制处理速度，避免对系统造成过大压力

### 2. 进度跟踪
- 使用Redis累加记录迁移总量，方便查看迁移进度
- 支持分布式环境下的进度同步
- 提供详细的处理日志

### 3. 失败处理
- 自动记录失败的迁移任务
- 支持失败重试机制
- 提供人工干预接口

## 配置参数

在系统配置中设置以下参数：

```properties
# 处方迁移每批大小（默认1000）
migration.prescription.page_size=1000

# 处方迁移延迟时间，单位毫秒（默认1000ms）
migration.prescription.delay.time=1000

# 处方迁移开关（1-开启，0-关闭）
migration.prescription.switch=1
```

## API接口

### 启动迁移任务

```http
POST /admin-api/kernel/common/history-prescription/migration
Content-Type: application/json

{
  "minId": 1,
  "maxId": 10000,
  "organSign": "ORG001"
}
```

## 数据流程

1. **任务分发阶段**
   - 接收迁移请求
   - 按页面大小分割任务
   - 发送MQ消息进行异步处理

2. **数据处理阶段**
   - 消费MQ消息
   - 查询老系统数据
   - 转换数据格式

3. **ES存储阶段**
   - 根据创建时间确定索引（如：inquiry_history_prescription_2023）
   - 根据机构号生成路由键
   - 按索引分组进行批量存储到Elasticsearch，提升写入性能

## 索引规则

### 索引命名
- 格式：`inquiry_history_prescription_{year}`
- 示例：`inquiry_history_prescription_2023`、`inquiry_history_prescription_2024`

### 路由规则
- 提取机构号中的数字部分作为路由键
- 最多取3位数字，避免路由键过长

## Redis键名规范

```
# 迁移数量累加记录（用于查看总迁移量）
migration:prescription:es:progress:{organSign}:count

# 失败记录
migration:prescription:fail:{organSign}:{timestamp}
```

## 失败记录表

系统会自动记录失败的迁移任务到数据库表 `saas_migration_prescription_fail_record`：

| 字段 | 类型 | 说明 |
|------|------|------|
| id | bigint | 主键ID |
| organ_sign | varchar(64) | 机构号 |
| min_id | bigint | 最小ID |
| max_id | bigint | 最大ID |
| fail_type | tinyint(2) | 失败类型（1-MQ发送失败，2-ES存储失败） |
| fail_reason | varchar(1000) | 失败原因 |
| retry_count | int(11) | 重试次数 |
| status | tinyint(2) | 处理状态（0-待处理，1-已处理，2-忽略） |

## 监控和运维

### 1. 进度查询
通过Redis查询迁移总量：
```bash
# 查看特定机构的迁移总量
redis-cli get "migration:prescription:es:progress:ORG001:count"

# 查看所有机构的迁移计数
redis-cli keys "migration:prescription:es:progress:*:count"
```

### 2. 失败任务查询
```sql
SELECT * FROM saas_migration_prescription_fail_record WHERE status = 0;
```

### 3. 日志监控
关注以下关键日志：
- 迁移任务启动日志
- MQ消息发送日志
- ES存储成功/失败日志
- 异常错误日志

## 性能优化

### 1. 批量写入优化
- 按ES索引分组进行批量写入，减少网络开销
- 使用路由键确保数据分布均匀
- 自动创建索引，避免手动维护

### 2. 开关控制
- 支持在迁移过程中动态关闭，每次循环都会检查开关状态
- 可以通过配置中心实时控制迁移开关

### 3. 进度监控
- Redis累加计数器实时显示迁移总量
- 分布式环境下多个消费者同时处理，提升整体速度

## 注意事项

1. **性能影响**：迁移过程会对老系统和ES集群产生一定负载，建议在业务低峰期执行
2. **数据一致性**：迁移过程中如果老系统数据发生变化，可能导致数据不一致
3. **存储空间**：确保ES集群有足够的存储空间
4. **网络稳定性**：长时间的迁移任务需要稳定的网络环境
5. **开关控制**：可以随时通过配置中心关闭迁移开关来停止任务

## 故障排查

### 常见问题

1. **MQ消息发送失败**
   - 检查RocketMQ连接状态
   - 确认Topic是否存在
   - 查看生产者配置

2. **ES存储失败**
   - 检查ES集群状态
   - 确认索引映射是否正确
   - 查看ES日志

3. **数据查询失败**
   - 检查老系统API连接
   - 确认查询参数是否正确
   - 查看网络连接状态

### 日志分析
重点关注包含以下关键字的日志：
- `处方迁移`
- `MigrationPrescription`
- `ERROR`
- `FAIL`
