package com.xyy.saas.inquiry.pharmacist.api.audit.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2025/04/21 13:16
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RemotePrescriptionAuditDto implements java.io.Serializable {

    /**
     * 处方编号
     */
    private String prescriptionPref;

    /**
     * 额度id
     */
    private Long costId;

    /**
     * 是否是审核处方
     */
    private boolean audit;

    /**
     * 处方信息 - 仅取消时候传入
     */
    private RemotePrescriptionDto remotePrescriptionDto;

    /**
     * 处方列表- 仅批量取消时候传入
     */
    private List<RemotePrescriptionDto> remotePrescriptionDtos;

}
