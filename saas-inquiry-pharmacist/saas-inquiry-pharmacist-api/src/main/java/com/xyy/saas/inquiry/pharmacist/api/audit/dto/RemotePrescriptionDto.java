package com.xyy.saas.inquiry.pharmacist.api.audit.dto;

import com.xyy.saas.inquiry.pojo.prescription.PrescriptionExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import java.time.LocalDateTime;

/**
 * @Author:chenxiaoyi
 * @Date:2025/04/21 13:16
 */
@Data
@Accessors(chain = true)
public class RemotePrescriptionDto implements java.io.Serializable {

    @Schema(description = "处方编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1517")
    private String pref;

    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1517")
    private Long tenantId;

    @Schema(description = "问诊编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "32287")
    private String inquiryPref;

    @Schema(description = "用药类型：0西药，1中药", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer medicineType;

    @Schema(description = "处方状态  0、待开方   1、已取消   2、待审核     3、审核中  4、审核通过   5、审核驳回", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer status;

    @Schema(description = "医师出方时间")
    private LocalDateTime outPrescriptionTime;

    @Schema(description = "问诊方式  1、图文问诊  2、视频问诊  3、电话问诊", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer inquiryWayType;

    @Schema(description = "问诊业务类型 1、药店问诊  2、远程审方", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer inquiryBizType;

    @Schema(description = "处方拓展字段", requiredMode = Schema.RequiredMode.REQUIRED)
    private PrescriptionExtDto ext;

}
