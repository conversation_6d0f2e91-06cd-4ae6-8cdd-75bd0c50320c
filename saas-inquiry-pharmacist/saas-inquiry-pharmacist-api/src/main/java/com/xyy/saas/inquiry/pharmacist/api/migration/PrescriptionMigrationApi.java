package com.xyy.saas.inquiry.pharmacist.api.migration;

import com.xyy.saas.inquiry.pojo.migration.MigrationPrescriptionReqDto;
import com.xyy.saas.inquiry.pojo.migration.PrescriptionMigrationInfoDto;
import java.util.List;

/**
 * @Author:chenxiaoyi
 * @Date:2025/08/20 10:17
 */
public interface PrescriptionMigrationApi {

    List<PrescriptionMigrationInfoDto> queryPrescriptionMigrationInfo(MigrationPrescriptionReqDto reqDto);


}
