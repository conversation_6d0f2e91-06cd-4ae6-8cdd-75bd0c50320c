package com.xyy.saas.inquiry.pharmacist.server.config.migration;

import com.xyy.saas.inquiry.pojo.migration.MigrationPrescriptionReqDto;
import com.xyy.saas.inquiry.pojo.migration.PrescriptionMigrationInfoDto;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.forward.InquiryPharmacistForwardRespVO;
import com.xyy.saas.inquiry.pharmacist.server.service.migration.dto.MigrationDrugStoreReqDto;
import com.xyy.saas.inquiry.pharmacist.server.service.migration.dto.MigrationEmployeeRespDto;
import com.xyy.saas.inquiry.pojo.ForwardResult;
import com.xyy.saas.inquiry.pojo.migration.MigrationDrugStoreRespDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationPackageRespDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationPatientDto;
import java.util.List;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

/**
 * @Author:chenxiaoyi
 * @Date:2024/11/27 20:41
 */


@HttpExchange(accept = "application/json", contentType = "application/json")
public interface InquiryMigrationForwardClient {

    /**
     * 转发门店列表
     *
     * @return ForwardResult
     */
    @PostExchange("/migration/forward/queryDrugStoreLists")
    ForwardResult<List<MigrationDrugStoreRespDto>> queryDrugStoreLists(@RequestBody MigrationDrugStoreReqDto dto);

    /**
     * 转发门店详情 + 配置信息
     *
     * @return ForwardResult
     */
    @GetExchange("/migration/forward/queryDrugStore")
    ForwardResult<MigrationDrugStoreRespDto> queryDrugStore(@RequestParam String organSign);

    /**
     * 设置门店迁移状态
     */
    @PostExchange("/migration/forward/updateDrugStoreMigrationStatus")
    ForwardResult<?> updateDrugStoreMigrationStatus(@RequestBody MigrationDrugStoreReqDto dto);

    /**
     * 获取门店员工列表
     */
    @GetExchange("/migration/forward/queryDrugStoreEmployee")
    ForwardResult<MigrationEmployeeRespDto> queryDrugStoreEmployee(@RequestParam String organSign);

    /**
     * 获取门店药师列表
     */
    @GetExchange("/migration/forward/queryDrugStorePharmacist")
    ForwardResult<List<InquiryPharmacistForwardRespVO>> queryDrugStorePharmacist(@RequestParam String organSign);


    /**
     * 获取门店患者 - 最近的1000条
     */
    @GetExchange("/migration/forward/queryDrugStorePatient")
    ForwardResult<List<MigrationPatientDto>> queryDrugStorePatient(@RequestParam String organSign);

    /**
     * 获取门店套餐
     */
    @GetExchange("/migration/forward/queryDrugStorePackage")
    ForwardResult<List<MigrationPackageRespDto>> queryDrugStorePackage(@RequestParam String organSign);


    /**
     * 设置门店迁移状态
     */
    @PostExchange("/migration/forward/queryPrescriptions")
    ForwardResult<List<PrescriptionMigrationInfoDto>> queryPrescriptionMigrationInfo(@RequestBody MigrationPrescriptionReqDto dto);

}
