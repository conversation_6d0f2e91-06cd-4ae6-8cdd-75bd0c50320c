package com.xyy.saas.inquiry.pharmacist.server.api.migration;

import com.xyy.saas.inquiry.pharmacist.api.migration.PrescriptionMigrationApi;
import com.xyy.saas.inquiry.pojo.migration.MigrationPrescriptionReqDto;
import com.xyy.saas.inquiry.pojo.migration.PrescriptionMigrationInfoDto;
import com.xyy.saas.inquiry.pharmacist.server.service.migration.InquiryMigrationForwardService;
import java.util.List;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * @Author:chenxiaoyi
 * @Date:2025/08/20 10:18
 */
@DubboService
public class PrescriptionMigrationApiImpl implements PrescriptionMigrationApi {

    @Resource
    private InquiryMigrationForwardService inquiryMigrationForwardService;

    @Override
    public List<PrescriptionMigrationInfoDto> queryPrescriptionMigrationInfo(MigrationPrescriptionReqDto reqDto) {
        return inquiryMigrationForwardService.queryPrescriptionMigrationInfo(reqDto);
    }
}
