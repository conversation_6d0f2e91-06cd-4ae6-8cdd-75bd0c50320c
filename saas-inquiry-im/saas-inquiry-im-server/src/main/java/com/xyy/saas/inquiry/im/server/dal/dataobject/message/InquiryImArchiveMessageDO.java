package com.xyy.saas.inquiry.im.server.dal.dataobject.message;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import java.time.LocalDateTime;

/**
 * 腾讯IM用户消息 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_im_archive_message")
@KeySequence("saas_inquiry_im_archive_message_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryImArchiveMessageDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 消息唯一key
     */
    private String msgKey;
    /**
     * 消息序列号，同一秒的消息,msgSeq 值越大消息越靠后
     */
    private Long msgSeq;
    /**
     * 问诊单号
     */
    private String inquiryPref;
    /**
     * 发送方IM用户名
     */
    private String msgFrom;
    /**
     * 接收方IM用户名
     */
    private String msgTo;
    /**
     * 消息发送时间
     */
    private LocalDateTime msgTime;
    /**
     * 消息已读位点 0-未读 1-已读
     */
    private Integer readOffset;
    /**
     * 消息体内容
     */
    private String msgBody;
    /**
     * 消息扩展内容
     */
    private String msgExt;

}