package com.xyy.saas.inquiry.common.controller.admin.migration;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.common.service.migration.MigrationPrescriptionService;
import com.xyy.saas.inquiry.pojo.migration.MigrationPrescriptionReqDto;
import com.xyy.saas.inquiry.util.ThreadPoolManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "后台 - 历史处方")
@RestController
@RequestMapping("/common/history-prescription")
@Validated
public class MigrationPrescriptionController {

    @Resource
    private MigrationPrescriptionService migrationPrescriptionService;

    @PostMapping("/migration")
    @Operation(summary = "历史处方迁移")
    public CommonResult<Boolean> migrationPrescription(@Valid @RequestBody MigrationPrescriptionReqDto reqDto) {
        ThreadPoolManager.execute(() -> migrationPrescriptionService.migrationPrescription(reqDto));
        return success(true);
    }

}