package com.xyy.saas.inquiry.common.convert.migration;

import com.xyy.saas.inquiry.common.mq.message.migration.dto.MigrationPrescriptionEventDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationPrescriptionReqDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 迁移转换
 */
@Mapper
public interface MigrationConvert {

    MigrationConvert INSTANCE = Mappers.getMapper(MigrationConvert.class);

    MigrationPrescriptionEventDto convert(MigrationPrescriptionReqDto reqDto);

    MigrationPrescriptionReqDto convertDto(MigrationPrescriptionEventDto msg);
}
