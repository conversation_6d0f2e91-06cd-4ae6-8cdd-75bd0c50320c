package com.xyy.saas.inquiry.common.entity.migration;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 处方迁移失败记录表
 * @Author:chenxiaoyi
 * @Date:2025/08/20 21:30
 */
@Data
@Accessors(chain = true)
@TableName("saas_migration_prescription_fail_record")
public class MigrationPrescriptionFailRecord {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 机构号
     */
    private String organSign;

    /**
     * 最小ID
     */
    private Long minId;

    /**
     * 最大ID
     */
    private Long maxId;

    /**
     * 失败类型 1-MQ发送失败 2-ES存储失败
     */
    private Integer failType;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 处理状态 0-待处理 1-已处理 2-忽略
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 处理时间
     */
    private LocalDateTime processTime;

    /**
     * 备注
     */
    private String remark;
}
