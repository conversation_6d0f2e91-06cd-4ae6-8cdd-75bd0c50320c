package com.xyy.saas.inquiry.common.service.migration.impl;

import com.xyy.saas.inquiry.common.entity.migration.MigrationPrescriptionFailRecord;
import com.xyy.saas.inquiry.common.mapper.migration.MigrationPrescriptionFailRecordMapper;
import com.xyy.saas.inquiry.common.service.migration.MigrationPrescriptionFailRecordService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 处方迁移失败记录服务实现类
 * @Author:chenxiaoyi
 * @Date:2025/08/20 21:37
 */
@Slf4j
@Service
public class MigrationPrescriptionFailRecordServiceImpl implements MigrationPrescriptionFailRecordService {

    @Resource
    private MigrationPrescriptionFailRecordMapper failRecordMapper;

    @Override
    public void saveFailRecord(MigrationPrescriptionFailRecord failRecord) {
        if (failRecord.getCreateTime() == null) {
            failRecord.setCreateTime(LocalDateTime.now());
        }
        if (failRecord.getUpdateTime() == null) {
            failRecord.setUpdateTime(LocalDateTime.now());
        }
        failRecordMapper.insert(failRecord);
        log.info("保存处方迁移失败记录成功，机构：{}，ID范围：{}-{}，失败类型：{}", 
            failRecord.getOrganSign(), failRecord.getMinId(), failRecord.getMaxId(), failRecord.getFailType());
    }

    @Override
    public void createMqFailRecord(String organSign, Long minId, Long maxId, String failReason) {
        MigrationPrescriptionFailRecord failRecord = new MigrationPrescriptionFailRecord()
            .setOrganSign(organSign)
            .setMinId(minId)
            .setMaxId(maxId)
            .setFailType(1) // MQ发送失败
            .setFailReason(failReason)
            .setRetryCount(0)
            .setStatus(0); // 待处理
        
        saveFailRecord(failRecord);
    }

    @Override
    public void createEsFailRecord(String organSign, Long minId, Long maxId, String failReason) {
        MigrationPrescriptionFailRecord failRecord = new MigrationPrescriptionFailRecord()
            .setOrganSign(organSign)
            .setMinId(minId)
            .setMaxId(maxId)
            .setFailType(2) // ES存储失败
            .setFailReason(failReason)
            .setRetryCount(0)
            .setStatus(0); // 待处理
        
        saveFailRecord(failRecord);
    }
}
