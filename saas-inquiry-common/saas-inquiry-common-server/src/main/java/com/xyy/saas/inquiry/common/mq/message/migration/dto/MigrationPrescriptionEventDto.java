package com.xyy.saas.inquiry.common.mq.message.migration.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class MigrationPrescriptionEventDto implements Serializable {

    /**
     * 最小id  >=
     */
    private Long minId;

    /**
     * 最大id <
     */
    private Long maxId;

    /**
     * 机构号
     */
    private String organSign;


}
