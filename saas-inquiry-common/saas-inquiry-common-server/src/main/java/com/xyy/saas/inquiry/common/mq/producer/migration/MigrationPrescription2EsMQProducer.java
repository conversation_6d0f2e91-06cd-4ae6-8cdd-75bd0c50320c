package com.xyy.saas.inquiry.common.mq.producer.migration;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.common.mq.message.migration.MigrationPrescriptionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 处方迁移到es
 */
@Component
@EventBusProducer(
    topic = MigrationPrescriptionEvent.TOPIC
)
@Slf4j
public class MigrationPrescription2EsMQProducer extends EventBusRocketMQTemplate {


}
