package com.xyy.saas.inquiry.common.service.migration;

import com.xyy.saas.inquiry.common.mq.message.migration.dto.MigrationPrescriptionEventDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationPrescriptionReqDto;

/**
 * @Author:ch<PERSON><PERSON><PERSON>i
 * @Date:2025/08/20 21:14
 */
public interface MigrationPrescriptionService {

    /**
     * 迁移处方
     *
     * @param reqDto
     */
    void migrationPrescription(MigrationPrescriptionReqDto reqDto);

    /**
     * 迁移处方到es
     *
     * @param msg
     */
    void migrationPrescription2Es(MigrationPrescriptionEventDto msg);

    /**
     * 查询迁移处方
     * @param reqDto
     */
    // void queryMigrationPrescription(MigrationPrescriptionReqDto reqDto);
}
