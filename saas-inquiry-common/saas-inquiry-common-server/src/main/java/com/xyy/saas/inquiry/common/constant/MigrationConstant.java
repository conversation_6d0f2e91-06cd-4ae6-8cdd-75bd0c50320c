package com.xyy.saas.inquiry.common.constant;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2024/12/12 13:55
 */
public class MigrationConstant {

    /**
     * 处方迁移每批大小
     */
    public static final String MIGRATION_PRESCRIPTION_PAGE_SIZE = "migration.prescription.page_size";
    /**
     * 处方迁移 延迟时间 单位:毫秒
     */
    public static final String MIGRATION_PRESCRIPTION_DELAY_TIME = "migration.prescription.delay.time";
    /**
     * 处方迁移开关
     */
    public static final String MIGRATION_PRESCRIPTION_SWITCH = "migration.prescription.switch";

    // Redis 键名前缀
    /**
     * 处方迁移进度记录键前缀
     */
    public static final String MIGRATION_PRESCRIPTION_PROGRESS_PREFIX = "migration:prescription:progress:";

    /**
     * 处方迁移到ES进度记录键前缀
     */
    public static final String MIGRATION_PRESCRIPTION_ES_PROGRESS_PREFIX = "migration:prescription:es:progress:";

    /**
     * 处方迁移失败记录键前缀
     */
    public static final String MIGRATION_PRESCRIPTION_FAIL_PREFIX = "migration:prescription:fail:";

    // ES 索引相关
    /**
     * 历史处方ES索引前缀
     */
    public static final String HISTORY_PRESCRIPTION_INDEX_PREFIX = "inquiry_history_prescription_";

}
