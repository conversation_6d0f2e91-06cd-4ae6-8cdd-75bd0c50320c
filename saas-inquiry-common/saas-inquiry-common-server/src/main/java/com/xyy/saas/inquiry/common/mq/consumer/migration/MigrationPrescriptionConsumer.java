package com.xyy.saas.inquiry.common.mq.consumer.migration;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.common.mq.message.migration.MigrationPrescriptionEvent;
import com.xyy.saas.inquiry.common.service.migration.MigrationPrescriptionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/26 11:03
 * @Description: 医生调度消费者
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_common_mq_consumer_migration_MigrationPrescriptionConsumer",
    topic = MigrationPrescriptionEvent.TOPIC)
public class MigrationPrescriptionConsumer {

    @Resource
    private MigrationPrescriptionService migrationPrescriptionService;

    @EventBusListener
    public void migrationPrescriptionConsumer(MigrationPrescriptionEvent event) {
        migrationPrescriptionService.migrationPrescription2Es(event.getMsg());
    }
}
