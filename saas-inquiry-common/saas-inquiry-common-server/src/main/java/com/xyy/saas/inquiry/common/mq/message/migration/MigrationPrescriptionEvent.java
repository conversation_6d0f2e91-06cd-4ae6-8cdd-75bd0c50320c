package com.xyy.saas.inquiry.common.mq.message.migration;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.inquiry.common.mq.message.migration.dto.MigrationPrescriptionEventDto;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 描述: 迁移处方消息
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class MigrationPrescriptionEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "MIGRATION_HISTORY_PRESCRIPTION";

    private MigrationPrescriptionEventDto msg;

    @JsonCreator
    public MigrationPrescriptionEvent(@JsonProperty("msg") MigrationPrescriptionEventDto msg) {
        this.msg = msg;
    }

    /**
     * 消息tag
     */
    @Override
    public String getTag() {
        return "";
    }
}
