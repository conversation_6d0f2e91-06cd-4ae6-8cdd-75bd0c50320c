package com.xyy.saas.inquiry.common.service.migration;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.xyy.saas.inquiry.common.constant.MigrationConstant;
import com.xyy.saas.inquiry.common.convert.migration.MigrationConvert;
import com.xyy.saas.inquiry.common.mq.message.migration.MigrationPrescriptionEvent;
import com.xyy.saas.inquiry.common.mq.message.migration.dto.MigrationPrescriptionEventDto;
import com.xyy.saas.inquiry.common.mq.producer.migration.MigrationPrescription2EsMQProducer;
import com.xyy.saas.inquiry.common.service.migration.MigrationPrescriptionFailRecordService;
import com.xyy.saas.inquiry.pharmacist.api.migration.PrescriptionMigrationApi;
import com.xyy.saas.inquiry.pojo.migration.MigrationPrescriptionReqDto;
import com.xyy.saas.inquiry.pojo.migration.PrescriptionMigrationInfoDto;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

/**
 * 处方迁移服务实现类
 * @Author:chenxiaoyi
 * @Date:2025/08/20 21:14
 */
@Slf4j
@Service
public class MigrationPrescriptionServiceImpl implements MigrationPrescriptionService {

    // Redis 键名常量
    private static final String MIGRATION_PRESCRIPTION_PROGRESS_KEY = "migration:prescription:progress:";
    private static final String MIGRATION_PRESCRIPTION_ES_PROGRESS_KEY = "migration:prescription:es:progress:";
    private static final String MIGRATION_PRESCRIPTION_FAIL_KEY = "migration:prescription:fail:";

    @Resource
    private PrescriptionMigrationApi prescriptionMigrationApi;

    @Resource
    protected StringRedisTemplate stringRedisTemplate;

    @Resource
    private ElasticsearchOperations elasticsearchOperations;

    @Resource
    private MigrationPrescription2EsMQProducer migrationPrescription2EsMQProducer;

    @Resource
    private ConfigApi configApi;

    @Resource
    private MigrationPrescriptionFailRecordService failRecordService;

    /**
     * 获取迁移延迟时间 默认1000ms
     */
    public Long getMigrationDelayTime() {
        return NumberUtils.toLong(configApi.getConfigValueByKey(MigrationConstant.MIGRATION_PRESCRIPTION_DELAY_TIME), 1000);
    }

    /**
     * 获取迁移分页大小 默认1000
     */
    private Integer getMigrationPageSize() {
        return NumberUtils.toInt(configApi.getConfigValueByKey(MigrationConstant.MIGRATION_PRESCRIPTION_PAGE_SIZE), 1000);
    }

    /**
     * 获取迁移开关 默认开启
     */
    private boolean getMigrationSwitch() {
        String byKey = configApi.getConfigValueByKey(MigrationConstant.MIGRATION_PRESCRIPTION_SWITCH);
        return StringUtils.equals(byKey, "1");
    }

    /**
     * 迁移休眠时间 ms
     *
     * @return
     */
    private Integer getMigrationSleepTime() {
        return 1000;
    }


    @Override
    public void migrationPrescription(MigrationPrescriptionReqDto reqDto) {
        log.info("开始处方迁移任务，参数：{}", JSONUtil.toJsonStr(reqDto));

        // 检查迁移开关
        if (!getMigrationSwitch()) {
            log.warn("处方迁移开关已关闭，跳过迁移任务");
            return;
        }

        Long minId = reqDto.getMinId();
        Long maxId = reqDto.getMaxId();
        String organSign = reqDto.getOrganSign();
        Integer pageSize = getMigrationPageSize();
        Long delayTime = getMigrationDelayTime();

        // 生成进度记录键
        String progressKey = MIGRATION_PRESCRIPTION_PROGRESS_KEY + organSign + ":" + minId + ":" + maxId;

        try {
            // 根据 minId 按照 getMigrationPageSize() 步长递增累加，存储到redis中
            Long currentMinId = minId;
            while (currentMinId < maxId) {
                Long currentMaxId = Math.min(currentMinId + pageSize, maxId);

                // 创建分页消息
                MigrationPrescriptionEventDto eventDto = new MigrationPrescriptionEventDto()
                        .setMinId(currentMinId)
                        .setMaxId(currentMaxId)
                        .setOrganSign(organSign);

                // 存储进度到Redis
                String progressValue = currentMinId + "-" + currentMaxId;
                stringRedisTemplate.opsForValue().set(progressKey + ":" + currentMinId, progressValue, 24, TimeUnit.HOURS);

                // 发送MQ消息
                migrationPrescription2EsMQProducer.sendMessage(
                    MigrationPrescriptionEvent.builder().msg(eventDto).build()
                );

                log.info("发送处方迁移MQ消息，机构：{}，ID范围：{}-{}", organSign, currentMinId, currentMaxId);

                // 延迟处理，避免对系统造成过大压力
                if (delayTime > 0) {
                    try {
                        Thread.sleep(delayTime);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.error("处方迁移延迟等待被中断", e);
                        break;
                    }
                }

                currentMinId = currentMaxId;
            }

            log.info("处方迁移任务分发完成，机构：{}，总范围：{}-{}", organSign, minId, maxId);

        } catch (Exception e) {
            log.error("处方迁移任务执行失败，参数：{}", JSONUtil.toJsonStr(reqDto), e);
            // 记录失败信息到数据库
            failRecordService.createMqFailRecord(organSign, minId, maxId, e.getMessage());
            // 同时记录到Redis用于快速查询
            String failKey = MIGRATION_PRESCRIPTION_FAIL_KEY + organSign + ":" + System.currentTimeMillis();
            stringRedisTemplate.opsForValue().set(failKey, JSONUtil.toJsonStr(reqDto), 7, TimeUnit.DAYS);
        }
    }


    @Override
    public void migrationPrescription2Es(MigrationPrescriptionEventDto msg) {
        log.info("开始处理处方迁移到ES，消息：{}", JSONUtil.toJsonStr(msg));

        Long minId = msg.getMinId();
        Long maxId = msg.getMaxId();
        String organSign = msg.getOrganSign();

        // 因为是分布式的，将msg中的id范围存储累加到一个新的redis中，方便记录进度
        String esProgressKey = MIGRATION_PRESCRIPTION_ES_PROGRESS_KEY + organSign + ":" + minId + ":" + maxId;

        try {
            // 记录开始处理时间
            stringRedisTemplate.opsForValue().set(esProgressKey + ":start",
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                24, TimeUnit.HOURS);

            // 执行老系统的查询
            List<PrescriptionMigrationInfoDto> migrationInfoDtos = prescriptionMigrationApi
                .queryPrescriptionMigrationInfo(MigrationConvert.INSTANCE.convertDto(msg));

            if (CollUtil.isEmpty(migrationInfoDtos)) {
                log.info("未查询到处方数据，机构：{}，ID范围：{}-{}", organSign, minId, maxId);
                stringRedisTemplate.opsForValue().set(esProgressKey + ":result", "empty", 24, TimeUnit.HOURS);
                return;
            }

            log.info("查询到处方数据{}条，机构：{}，ID范围：{}-{}", migrationInfoDtos.size(), organSign, minId, maxId);

            // 将数据存储到ES中
            // 1.根据createTime的年部分区分索引，索引应为 inquiry_history_prescription_2023、inquiry_history_prescription_2024 等
            // 2.根据organSign的数字部分作为路由键
            int successCount = 0;
            int failCount = 0;

            for (PrescriptionMigrationInfoDto prescriptionInfo : migrationInfoDtos) {
                try {
                    // 根据创建时间确定索引名称
                    String indexName = getIndexNameByCreateTime(prescriptionInfo.getCreateTime());

                    // 根据organSign获取路由键
                    String routing = getRoutingByOrganSign(organSign);

                    // 构建文档ID，使用处方ID
                    String documentId = String.valueOf(prescriptionInfo.getId());

                    // 将处方信息转换为ES文档
                    Document document = Document.create();
                    document.putAll(JSONUtil.parseObj(prescriptionInfo));

                    // 存储到ES
                    IndexCoordinates indexCoordinates = IndexCoordinates.of(indexName);

                    // 确保索引存在
                    IndexOperations indexOps = elasticsearchOperations.indexOps(indexCoordinates);
                    if (!indexOps.exists()) {
                        indexOps.create();
                        log.info("创建ES索引：{}", indexName);
                    }

                    // 保存文档，使用路由
                    elasticsearchOperations.save(document.toMap(), indexCoordinates);

                    successCount++;

                } catch (Exception e) {
                    failCount++;
                    log.error("处方数据存储到ES失败，处方ID：{}，机构：{}", prescriptionInfo.getId(), organSign, e);
                }
            }

            // 记录处理结果
            String result = String.format("success:%d,fail:%d,total:%d", successCount, failCount, migrationInfoDtos.size());
            stringRedisTemplate.opsForValue().set(esProgressKey + ":result", result, 24, TimeUnit.HOURS);
            stringRedisTemplate.opsForValue().set(esProgressKey + ":end",
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                24, TimeUnit.HOURS);

            log.info("处方迁移到ES完成，机构：{}，ID范围：{}-{}，成功：{}，失败：{}",
                organSign, minId, maxId, successCount, failCount);

        } catch (Exception e) {
            log.error("处方迁移到ES执行失败，消息：{}", JSONUtil.toJsonStr(msg), e);

            // 记录失败信息到数据库
            failRecordService.createEsFailRecord(organSign, minId, maxId, e.getMessage());

            // 如果失败，将失败的范围id存储到Redis中，方便人工介入
            String failKey = MIGRATION_PRESCRIPTION_FAIL_KEY + "es:" + organSign + ":" + System.currentTimeMillis();
            String failInfo = String.format("minId:%d,maxId:%d,organSign:%s,error:%s",
                minId, maxId, organSign, e.getMessage());
            stringRedisTemplate.opsForValue().set(failKey, failInfo, 7, TimeUnit.DAYS);

            // 记录失败状态
            stringRedisTemplate.opsForValue().set(esProgressKey + ":error", e.getMessage(), 24, TimeUnit.HOURS);
        }
    }

    /**
     * 根据创建时间获取ES索引名称
     * 格式：inquiry_history_prescription_yyyy
     */
    private String getIndexNameByCreateTime(java.util.Date createTime) {
        if (createTime == null) {
            // 如果创建时间为空，使用当前年份
            return "inquiry_history_prescription_" + LocalDateTime.now().getYear();
        }

        // 提取年份
        LocalDateTime dateTime = createTime.toInstant()
            .atZone(java.time.ZoneId.systemDefault())
            .toLocalDateTime();

        return "inquiry_history_prescription_" + dateTime.getYear();
    }

    /**
     * 根据机构号获取路由键
     * 提取organSign中的数字部分作为路由键
     */
    private String getRoutingByOrganSign(String organSign) {
        if (StrUtil.isBlank(organSign)) {
            return "0";
        }

        // 提取数字部分
        String numbers = organSign.replaceAll("[^0-9]", "");
        if (StrUtil.isBlank(numbers)) {
            return "0";
        }

        // 取最后几位数字作为路由键，避免路由键过长
        if (numbers.length() > 3) {
            numbers = numbers.substring(numbers.length() - 3);
        }

        return numbers;
    }
}
