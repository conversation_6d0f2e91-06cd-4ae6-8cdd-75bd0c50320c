package com.xyy.saas.inquiry.common.service.migration;

import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.xyy.saas.inquiry.common.constant.MigrationConstant;
import com.xyy.saas.inquiry.common.convert.migration.MigrationConvert;
import com.xyy.saas.inquiry.common.mq.message.migration.MigrationPrescriptionEvent;
import com.xyy.saas.inquiry.common.mq.message.migration.dto.MigrationPrescriptionEventDto;
import com.xyy.saas.inquiry.common.mq.producer.migration.MigrationPrescription2EsMQProducer;
import com.xyy.saas.inquiry.pharmacist.api.migration.PrescriptionMigrationApi;
import com.xyy.saas.inquiry.pojo.migration.MigrationPrescriptionReqDto;
import com.xyy.saas.inquiry.pojo.migration.PrescriptionMigrationInfoDto;
import jakarta.annotation.Resource;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * @Author:chenxiaoyi
 * @Date:2025/08/20 21:14
 */
@Resource
public class MigrationPrescriptionServiceImpl implements MigrationPrescriptionService {

    @Resource
    private PrescriptionMigrationApi prescriptionMigrationApi;

    @Resource
    protected StringRedisTemplate stringRedisTemplate;

    @Resource
    private ElasticsearchOperations elasticsearchOperations;

    @Resource
    private MigrationPrescription2EsMQProducer migrationPrescription2EsMQProducer;

    @Resource
    private ConfigApi configApi;

    /**
     * 获取迁移延迟时间 默认1000ms
     */
    public Long getMigrationDelayTime() {
        return NumberUtils.toLong(configApi.getConfigValueByKey(MigrationConstant.MIGRATION_PRESCRIPTION_DELAY_TIME), 1000);
    }

    /**
     * 获取迁移分页大小 默认1000
     */
    private Integer getMigrationPageSize() {
        return NumberUtils.toInt(configApi.getConfigValueByKey(MigrationConstant.MIGRATION_PRESCRIPTION_PAGE_SIZE), 1000);
    }

    /**
     * 获取迁移开关 默认开启
     */
    private boolean getMigrationSwitch() {
        String byKey = configApi.getConfigValueByKey(MigrationConstant.MIGRATION_PRESCRIPTION_SWITCH);
        return StringUtils.equals(byKey, "1");
    }

    /**
     * 迁移休眠时间 ms
     *
     * @return
     */
    private Integer getMigrationSleepTime() {
        return 1000;
    }


    @Override
    public void migrationPrescription(MigrationPrescriptionReqDto reqDto) {

        migrationPrescription2EsMQProducer.sendMessage(MigrationPrescriptionEvent.builder().msg(MigrationConvert.INSTANCE.convert(reqDto)).build());
    }


    @Override
    public void migrationPrescription2Es(MigrationPrescriptionEventDto msg) {

        List<PrescriptionMigrationInfoDto> migrationInfoDtos = prescriptionMigrationApi.queryPrescriptionMigrationInfo(MigrationConvert.INSTANCE.convertDto(msg));

    }
}
