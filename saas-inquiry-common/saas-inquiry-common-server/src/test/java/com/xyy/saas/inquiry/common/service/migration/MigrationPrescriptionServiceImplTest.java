package com.xyy.saas.inquiry.common.service.migration;

import com.xyy.saas.inquiry.pojo.migration.MigrationPrescriptionReqDto;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import jakarta.annotation.Resource;

/**
 * 处方迁移服务测试类
 * @Author:chenxia<PERSON>i
 * @Date:2025/08/20 21:45
 */
@SpringBootTest
@ActiveProfiles("test")
public class MigrationPrescriptionServiceImplTest {

    @Resource
    private MigrationPrescriptionService migrationPrescriptionService;

    @Test
    public void testMigrationPrescription() {
        // 创建测试请求
        MigrationPrescriptionReqDto reqDto = new MigrationPrescriptionReqDto()
            .setMinId(1L)
            .setMaxId(1000L)
            .setOrganSign("TEST001");

        // 执行迁移
        migrationPrescriptionService.migrationPrescription(reqDto);
        
        System.out.println("处方迁移任务已提交");
    }

    @Test
    public void testMigrationPrescriptionWithSwitch() {
        // 测试迁移开关功能
        MigrationPrescriptionReqDto reqDto = new MigrationPrescriptionReqDto()
            .setMinId(1L)
            .setMaxId(100L)
            .setOrganSign("TEST002");

        // 执行迁移（会在循环中检查开关状态）
        migrationPrescriptionService.migrationPrescription(reqDto);

        System.out.println("处方迁移任务已提交（包含开关检查）");
    }

    @Test
    public void testGetIndexNameByCreateTime() {
        // 这里可以添加对工具方法的单元测试
        // 由于方法是private的，可以通过反射或者将方法改为protected来测试
    }
}
