-- 处方迁移失败记录表
DROP TABLE IF EXISTS saas_migration_prescription_fail_record;
CREATE TABLE saas_migration_prescription_fail_record
(
    id           bigint auto_increment comment '主键ID' primary key,
    organ_sign   varchar(64)  NOT NULL DEFAULT '' COMMENT '机构号',
    min_id       bigint       NOT NULL DEFAULT 0 COMMENT '最小ID',
    max_id       bigint       NOT NULL DEFAULT 0 COMMENT '最大ID',
    fail_type    tinyint(2)   NOT NULL DEFAULT 0 COMMENT '失败类型 1-MQ发送失败 2-ES存储失败',
    fail_reason  varchar(1000) NOT NULL DEFAULT '' COMMENT '失败原因',
    retry_count  int(11)      NOT NULL DEFAULT 0 COMMENT '重试次数',
    status       tinyint(2)   NOT NULL DEFAULT 0 COMMENT '处理状态 0-待处理 1-已处理 2-忽略',
    create_time  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    process_time datetime     NULL COMMENT '处理时间',
    remark       varchar(500) NOT NULL DEFAULT '' COMMENT '备注'
) COMMENT ='处方迁移失败记录表';

-- 创建索引
CREATE INDEX idx_organ_sign ON saas_migration_prescription_fail_record (organ_sign) COMMENT '机构号索引';
CREATE INDEX idx_fail_type ON saas_migration_prescription_fail_record (fail_type) COMMENT '失败类型索引';
CREATE INDEX idx_status ON saas_migration_prescription_fail_record (status) COMMENT '处理状态索引';
CREATE INDEX idx_create_time ON saas_migration_prescription_fail_record (create_time) COMMENT '创建时间索引';
