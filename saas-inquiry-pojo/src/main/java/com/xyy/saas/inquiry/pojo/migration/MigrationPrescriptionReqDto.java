package com.xyy.saas.inquiry.pojo.migration;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.AssertTrue;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class MigrationPrescriptionReqDto implements Serializable {

    /**
     * 最小id  >=
     */
    private Long minId;

    /**
     * 最大id <
     */
    private Long maxId;

    /**
     * 机构号
     */
    private String organSign;


    @AssertTrue(message = "两个迁移id或者organSign不可同时为空")
    @JsonIgnore
    public boolean migrationValid() {
        return (minId != null && maxId != null) || StringUtils.isNotBlank(organSign);
    }

}
