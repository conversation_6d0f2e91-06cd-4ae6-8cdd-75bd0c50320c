package com.xyy.saas.inquiry.patient.api;

import com.xyy.saas.inquiry.patient.api.inquiry.InquiryRemoteAuditApi;
import com.xyy.saas.inquiry.patient.service.inquiry.handle.InquiryCancelHandle;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import java.util.List;

/**
 * @Author: xucao
 * @DateTime: 2025/4/24 16:13
 * @Description: 远程问诊api实现类
 **/
@DubboService
public class InquiryRemoteAuditApiImpl implements InquiryRemoteAuditApi {

    @Resource
    private InquiryCancelHandle inquiryCancelHandle;

    /**
     * 批量取消远程审方问诊
     *
     * @param inquiryPrefList 远程问诊编码集合
     */
    @Override
    public void batchCancelRemoteInquiry(List<String> inquiryPrefList) {
        if(CollectionUtils.isEmpty(inquiryPrefList)){
            return;
        }
        inquiryCancelHandle.batchCancelRemoteInquiry(inquiryPrefList);
    }
}
